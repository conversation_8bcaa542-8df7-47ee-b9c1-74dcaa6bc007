<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Assistant</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <div class="app-container">
        <!-- Sources Panel -->
        <aside class="sources-panel" id="sources-panel">
            <div class="sources-header">
                <div class="sources-title">Sources</div>
                <button class="panel-toggle-btn" id="panel-toggle" title="Toggle panel">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                        <path d="M15 18l-6-6 6-6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </button>
            </div>

            <div class="sources-actions">
                <button class="action-btn" id="add-source-btn">
                    <span>+</span> Add
                </button>
                <button class="action-btn" id="youtube-btn">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                        <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136C4.495 20.455 12 20.455 12 20.455s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z" fill="currentColor"/>
                    </svg>
                    YouTube
                </button>
            </div>



            <div class="sources-list" id="sources-list">
                <!-- Sources will be populated here -->
            </div>

            <div class="sources-footer">
                <div class="sources-count" id="sources-count">0 sources available</div>
            </div>
        </aside>

        <!-- Settings Panel -->
        <aside class="settings-panel collapsed" id="settings-panel">
            <div class="settings-header">
                <div class="settings-title">Settings</div>
                <button class="panel-toggle-btn" id="settings-panel-toggle" title="Close settings">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                        <path d="M9 18l6-6-6-6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </button>
            </div>

            <div class="settings-content">
                <div class="setting-group">
                    <div class="setting-label">Font Size</div>
                    <div class="font-size-buttons">
                        <button class="font-size-btn" data-size="small">Small</button>
                        <button class="font-size-btn active" data-size="medium">Medium</button>
                        <button class="font-size-btn" data-size="large">Large</button>
                    </div>
                </div>

                <div class="setting-group">
                    <div class="setting-label">Account</div>
                    <div class="font-size-buttons">
                        <button class="font-size-btn" id="logout-btn">Logout</button>
                    </div>
                </div>
            </div>
        </aside>

        <!-- Hidden file input for PDF upload -->
        <input type="file" id="pdf-upload-input" accept="application/pdf" style="display: none;">

        <!-- Floating toggle button (shown when panel is collapsed) -->
        <button class="floating-toggle-btn hidden" id="floating-toggle-btn" title="Show sources">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <path d="M9 18l6-6-6-6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
        </button>

        <!-- Main Content Area -->
        <div class="main-content">
            <!-- Header with branding and model selector -->
            <header class="app-header">
                <div class="header-left">
                    <button class="mobile-sources-toggle" id="mobile-sources-toggle" title="Toggle sources">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                            <path d="M3 12h18M3 6h18M3 18h18" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                        </svg>
                    </button>
                    <h1 class="app-title">AI Assistant</h1>
                    <!-- Model selector removed: model forced to default in backend -->
                </div>
                <div class="header-right">
                    <button class="external-link-btn" id="external-link-btn" title="Open External Service">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                            <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <polyline points="15,3 21,3 21,9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <line x1="10" y1="14" x2="21" y2="3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        Task Assign
                    </button>
                    <button class="settings-toggle-btn" id="settings-toggle" title="Settings">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                            <path d="M12 15a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z" stroke="currentColor" stroke-width="2"/>
                            <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1Z" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </button>
                </div>
            </header>

            <!-- Main chat area -->
            <main class="chat-main">
            <div class="chat-container" id="chat-container">
                <!-- Welcome message will be shown when no messages -->
                <div class="welcome-section" id="welcome-section">
                    <div class="welcome-message">
                        <h2>Good to see you, DH.</h2>
                        <p>How can I help you today?</p>
                    </div>
                </div>
                <!-- Chat messages will be added here -->
            </div>
        </main>

            <!-- Input area -->
            <footer class="input-footer">
            <div class="input-container">
                <div class="input-wrapper">
                    <input
                        type="text"
                        id="question-input"
                        placeholder="Ask a question"
                        autocomplete="off"
                        class="message-input"
                    >
                    <div id="autocomplete-dropdown" class="autocomplete-dropdown hidden">
                        <!-- Autocomplete suggestions will be populated here -->
                    </div>
                </div>
                <button id="speech-button" class="mic-button" title="Voice input">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z" fill="currentColor"/>
                        <path d="M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z" fill="currentColor"/>
                    </svg>
                </button>
            </div>

            <!-- Recording indicator -->
            <div class="recording-indicator hidden" id="recording-indicator">
                <div class="recording-animation">
                    <div class="pulse"></div>
                </div>
                <span>Recording... Release to stop</span>
            </div>

            <!-- Status messages -->
            <div id="status-message" class="status-message hidden">
                <!-- Status messages will appear here -->
            </div>
            </footer>
        </div>
    </div>

    <!-- Loading overlay -->
    <div id="loading-overlay" class="loading-overlay hidden">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p id="loading-text">Processing...</p>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
</body>
</html>
